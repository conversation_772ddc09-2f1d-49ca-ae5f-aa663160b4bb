#!/usr/bin/env python

import os
import re
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field

from .aider_integration_service import AiderIntegrationService


@dataclass
class SymbolRequest:
    """Represents a symbol requested by the LLM."""
    type: str  # method_definition, class_definition, function_definition, etc.
    name: str  # The name of the symbol, e.g., "AuthService.login_user"
    file_hint: Optional[str] = None  # Optional hint about which file contains the symbol


@dataclass
class ContextRequest:
    """Represents a context request from the LLM."""
    original_user_query_context: str
    symbols_of_interest: List[SymbolRequest]
    reason_for_request: str = ""  # Make this optional with default value


@dataclass
class IRContextRequest:
    """Represents an intelligent context request using IR and ICD."""
    user_query: str
    task_description: str
    task_type: str = "general_analysis"  # debugging, feature_development, etc.
    focus_entities: List[str] = None
    max_tokens: int = 8000
    include_ir_slices: bool = True
    include_code_context: bool = True
    # LLM compatibility options
    llm_friendly: bool = False  # Generate compact package for standard LLMs
    max_output_chars: int = 30000  # Maximum characters for LLM compatibility
    max_entities: int = 10  # Maximum entities for compact mode


class ContextRequestHandler:
    """
    Handles context requests from the LLM, extracting the requested symbols
    and their dependencies using the surgical extraction system.
    """

    # Class-level IR cache to share across instances
    _ir_cache = {}
    _ir_cache_timestamps = {}
    _ir_cache_ttl = 3600  # 1 hour

    def __init__(self, project_path: str, aider_service: Optional[AiderIntegrationService] = None):
        """
        Initialize the context request handler.

        Args:
            project_path: Path to the project root
            aider_service: Optional AiderIntegrationService instance
        """
        self.project_path = project_path
        self.aider_service = aider_service or AiderIntegrationService()
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_ttl = 3600  # 1 hour

    @classmethod
    def _get_ir_from_cache(cls, project_path: str) -> Optional[Dict]:
        """Get IR data from the class-level cache if it exists and is not expired."""
        import time

        if project_path in cls._ir_cache:
            timestamp = cls._ir_cache_timestamps.get(project_path, 0)
            if time.time() - timestamp < cls._ir_cache_ttl:
                print("📋 Using cached IR data")
                return cls._ir_cache[project_path]
            else:
                # Cache expired, remove it
                print("⏰ IR cache expired, will regenerate")
                cls._ir_cache.pop(project_path, None)
                cls._ir_cache_timestamps.pop(project_path, None)

        return None

    @classmethod
    def _update_ir_cache(cls, project_path: str, ir_data: Dict):
        """Update the class-level IR cache."""
        import time

        cls._ir_cache[project_path] = ir_data
        cls._ir_cache_timestamps[project_path] = time.time()
        print(f"💾 Cached IR data for project: {project_path}")

    @classmethod
    def preload_ir_data(cls, project_path: str) -> Optional[Dict]:
        """
        Preload IR data for a project. This can be called at startup.

        Args:
            project_path: Path to the project root

        Returns:
            The generated IR data, or None if generation failed
        """
        print(f"🚀 Preloading IR data for project: {project_path}")

        try:
            # Import the standalone AiderIntegrationService
            import sys
            import os

            # Add the root directory to path to import the standalone service
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            if root_dir not in sys.path:
                sys.path.insert(0, root_dir)

            from aider_integration_service import AiderIntegrationService as StandaloneService
            standalone_service = StandaloneService()

            start_time = time.time()
            ir_data = standalone_service.generate_mid_level_ir(project_path)
            generation_time = time.time() - start_time

            # Cache the generated data
            cls._update_ir_cache(project_path, ir_data)

            print(f"✅ IR preloading completed in {generation_time:.2f} seconds")
            return ir_data

        except Exception as e:
            print(f"❌ Failed to preload IR data: {e}")
            return None

    def _get_from_cache(self, cache_key: str) -> Any:
        """Get a value from the cache if it exists and is not expired."""
        if cache_key in self.cache:
            timestamp = self.cache_timestamps.get(cache_key, 0)
            if (timestamp + self.cache_ttl) > time.time():
                return self.cache[cache_key]
        return None

    def _update_cache(self, cache_key: str, value: Any) -> None:
        """Update the cache with a new value."""
        self.cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

    def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
        """
        Parse a context request from the LLM response.

        Args:
            request_text: The text containing the context request

        Returns:
            A ContextRequest object or None if the request is invalid
        """
        try:
            # Extract the JSON object from the request text using more robust patterns
            # First try the standard pattern with double closing braces
            pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}\}'
            match = re.search(pattern, request_text, re.DOTALL)
            if not match:
                # Try with a single closing brace (more common format)
                pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
                match = re.search(pattern, request_text, re.DOTALL)
                if not match:
                    # Try with any content after CONTEXT_REQUEST:
                    pattern = r'\{CONTEXT_REQUEST:\s*(.*)'
                    match = re.search(pattern, request_text, re.DOTALL)
                    if not match:
                        # Try with just the keyword
                        pattern = r'\{CONTEXT_REQUEST'
                        if re.search(pattern, request_text, re.DOTALL):
                            # Found the keyword but couldn't extract content properly
                            print("Found CONTEXT_REQUEST keyword but couldn't extract content properly")
                            # Create a minimal valid context request
                            return ContextRequest(
                                original_user_query_context="Extracted from malformed CONTEXT_REQUEST",
                                symbols_of_interest=[],
                                reason_for_request="Malformed CONTEXT_REQUEST detected"
                            )
                        return None

            # Get the matched content
            json_str = match.group(1).strip()

            # Clean up the JSON string
            # Remove any trailing }} that might be part of the CONTEXT_REQUEST format
            json_str = json_str.rstrip('}')

            # Ensure it's a valid JSON object
            if not json_str.startswith('{'):
                json_str = '{' + json_str
            if not json_str.endswith('}'):
                json_str = json_str + '}'

            # Replace any escaped quotes
            json_str = json_str.replace('\\"', '"')

            # Fix backslash escaping issues in file paths
            # Replace backslashes with forward slashes in file paths
            json_str = re.sub(r'"([^"]*\\[^"]*)"', lambda m: '"' + m.group(1).replace('\\', '/') + '"', json_str)

            # Try to parse the JSON
            try:
                request_data = json.loads(json_str)
            except json.JSONDecodeError as e:
                print(f"JSON parsing error: {e}")
                print(f"Problematic JSON: {json_str}")

                # Try to fix common JSON formatting issues
                # Replace single quotes with double quotes
                json_str = json_str.replace("'", '"')
                # Fix unquoted keys
                json_str = re.sub(r'(\w+):', r'"\1":', json_str)
                # Fix backslash escaping issues again after quote replacement
                json_str = re.sub(r'"([^"]*\\[^"]*)"', lambda m: '"' + m.group(1).replace('\\', '/') + '"', json_str)

                try:
                    request_data = json.loads(json_str)
                    print("✅ Fixed JSON parsing after format corrections")
                except json.JSONDecodeError as e2:
                    print(f"❌ Still failed after corrections: {e2}")
                    print(f"Final JSON attempt: {json_str}")
                    raise e2

            # Create the ContextRequest object
            symbols = []
            symbols_of_interest = request_data.get('symbols_of_interest', [])

            # Check if symbols_of_interest is in the wrong format (array of strings instead of objects)
            if symbols_of_interest and isinstance(symbols_of_interest[0], str):
                # LLM used wrong format - provide guidance
                print("❌ CONTEXT_REQUEST FORMAT ERROR:")
                print("symbols_of_interest should be an array of objects, not strings!")
                print("❌ Wrong format:", symbols_of_interest)
                print("✅ Correct format should be:")
                print('[{"type": "method_definition", "name": "function_name", "file_hint": "path/to/file.py"}]')

                # Try to convert the strings to proper format by guessing
                for symbol_name in symbols_of_interest:
                    # Skip generic terms that aren't actual symbols
                    if symbol_name in ['position', 'conditions', 'exit', 'trading', 'close']:
                        continue

                    # Determine the type based on naming patterns
                    symbol_type = "function_definition"
                    if symbol_name and symbol_name[0].isupper():
                        symbol_type = "class_definition"
                    elif '.' in symbol_name:
                        symbol_type = "method_definition"

                    symbols.append(SymbolRequest(
                        type=symbol_type,
                        name=symbol_name,
                        file_hint=None  # No file hint available from string format
                    ))
            else:
                # Correct format - process normally
                for symbol_data in symbols_of_interest:
                    if isinstance(symbol_data, dict):
                        # Handle both old file_hint format and new directory_name/file_name format
                        file_hint = symbol_data.get('file_hint')
                        directory_name = symbol_data.get('directory_name')
                        file_name = symbol_data.get('file_name')

                        # If using new format, construct file_hint from directory_name and file_name
                        if directory_name and file_name:
                            file_hint = f"{directory_name}/{file_name}"
                        elif directory_name and not file_name:
                            file_hint = directory_name
                        elif file_name and not directory_name:
                            file_hint = file_name
                        # Otherwise use the existing file_hint value

                        symbols.append(SymbolRequest(
                            type=symbol_data.get('type', 'unknown'),
                            name=symbol_data.get('name', ''),
                            file_hint=file_hint
                        ))
                    else:
                        print(f"Warning: Invalid symbol data format: {symbol_data}")

            # Provide a meaningful default for reason_for_request if missing
            reason_for_request = request_data.get('reason_for_request', '')
            if not reason_for_request:
                # Generate a default reason based on the user query
                user_query = request_data.get('original_user_query_context', '')
                if user_query:
                    reason_for_request = f"To answer the user's question: {user_query}"
                else:
                    reason_for_request = "To provide code context for the user's query"

            return ContextRequest(
                original_user_query_context=request_data.get('original_user_query_context', ''),
                symbols_of_interest=symbols,
                reason_for_request=reason_for_request
            )
        except Exception as e:
            print(f"Error parsing context request: {e}")
            return None

    def _find_file_for_symbol(self, symbol: SymbolRequest) -> Optional[str]:
        """
        Find the file that contains the requested symbol.

        Args:
            symbol: The symbol to find

        Returns:
            The path to the file containing the symbol, or None if not found
        """
        # Check if the symbol is referring to an installed Aider package (not local development)
        if symbol.name.startswith('aider.') and not (symbol.file_hint and ('aider-main' in symbol.file_hint or 'aider/' in symbol.file_hint)):
            print(f"Warning: Symbol {symbol.name} appears to be part of the installed Aider package, not your project.")
            print(f"The system cannot access installed Aider package files directly.")
            return None

        # If we have a file hint, try that first
        if symbol.file_hint:
            # Check if the file exists
            file_path = os.path.join(self.project_path, symbol.file_hint)
            if os.path.exists(file_path):
                return symbol.file_hint
            else:
                print(f"Warning: File hint {symbol.file_hint} does not exist at {file_path}")

                # Try to find the file using RepoMap-style discovery
                discovered_file = self._discover_file_like_repomap(symbol.file_hint)
                if discovered_file:
                    rel_path = os.path.relpath(discovered_file, self.project_path)
                    print(f"Found alternative file at: {rel_path}")
                    return rel_path

        # Extract the symbol name (handle class.method format)
        symbol_parts = symbol.name.split('.')
        if len(symbol_parts) > 1:
            # This is a class.method or module.function format
            class_name = symbol_parts[0]
            method_name = symbol_parts[1]

            # Check if this is an Aider package class
            if class_name == 'aider' or class_name.startswith('aider.'):
                print(f"Warning: Class {class_name} appears to be part of the Aider package, not your project.")
                print(f"The system cannot access Aider package files directly.")
                return None

            # Try to find the file defining the class
            class_file = self.aider_service.find_file_defining_symbol(self.project_path, class_name)
            if class_file:
                return class_file

        # Try to find the file defining the symbol directly
        symbol_name = symbol_parts[-1]  # Use the last part of the symbol name
        symbol_file = self.aider_service.find_file_defining_symbol(self.project_path, symbol_name)
        if symbol_file:
            return symbol_file

        print(f"Warning: Could not find file for symbol: {symbol.name}")
        return None

    def _discover_file_like_repomap(self, file_hint: str) -> Optional[str]:
        """
        Discover files using the same logic as RepoMap to ensure consistency.
        This replicates the file discovery mechanism from aider.repomap.
        """
        try:
            # Normalize the file hint (handle both forward and backward slashes)
            normalized_hint = file_hint.replace('\\', '/').strip('/')
            hint_parts = normalized_hint.split('/')
            target_filename = hint_parts[-1]
            target_dir_parts = hint_parts[:-1] if len(hint_parts) > 1 else []

            # Walk the directory tree like RepoMap does
            for root, dirs, files in os.walk(self.project_path):
                # Skip hidden directories and common directories to ignore (like RepoMap)
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', 'venv', '.git', '__pycache__']]

                # Check if target file is in this directory
                if target_filename in files:
                    candidate_path = os.path.join(root, target_filename)
                    rel_path = os.path.relpath(candidate_path, self.project_path)

                    # Normalize the relative path for comparison
                    normalized_rel_path = rel_path.replace('\\', '/').strip('/')

                    # Exact match
                    if normalized_rel_path == normalized_hint:
                        return candidate_path

                    # If we have directory parts, check if they match
                    if target_dir_parts:
                        rel_parts = normalized_rel_path.split('/')[:-1]  # Exclude filename

                        # Check if the directory structure matches (partial or full)
                        if self._directory_parts_match(target_dir_parts, rel_parts):
                            return candidate_path
                    else:
                        # No directory specified, just filename - return first match
                        return candidate_path

            return None

        except Exception as e:
            print(f"Warning: Error in file discovery for {file_hint}: {e}")
            return None

    def _directory_parts_match(self, target_parts: list, candidate_parts: list) -> bool:
        """
        Check if directory parts match, allowing for partial matches.
        This handles cases where the hint might be a subdirectory path.
        """
        if not target_parts:
            return True

        if len(target_parts) > len(candidate_parts):
            return False

        # Convert to strings for easier comparison
        target_str = '/'.join(target_parts)
        candidate_str = '/'.join(candidate_parts)

        # Check if target is a substring of candidate (anywhere in the path)
        # This allows for both prefix and suffix matching
        return target_str in candidate_str or candidate_str.endswith(target_str) or candidate_str.startswith(target_str)

    def _extract_symbol_content(self, symbol: SymbolRequest) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        Extract the content of a symbol using the surgical extraction system.

        Args:
            symbol: The symbol to extract

        Returns:
            A tuple of (file_path, symbol_name, content) or (None, None, None) if extraction failed
        """
        # Find the file containing the symbol
        file_path = self._find_file_for_symbol(symbol)
        if not file_path:
            return None, None, None

        # Extract the symbol name (handle class.method format)
        symbol_parts = symbol.name.split('.')
        if len(symbol_parts) > 1:
            # This is a class.method or module.function format
            class_name = symbol_parts[0]
            method_name = symbol_parts[1]

            # Try to extract the method content using the full qualified name
            content = self.aider_service.extract_symbol_content(symbol.name, file_path, self.project_path)
            if content:
                return file_path, symbol.name, content

            # Try to extract the method content
            content = self.aider_service.extract_symbol_content(method_name, file_path, self.project_path)
            if content:
                return file_path, method_name, content

            # If that fails, try to extract the class content
            content = self.aider_service.extract_symbol_content(class_name, file_path, self.project_path)
            if content:
                return file_path, class_name, content
        else:
            # This is a simple symbol name
            symbol_name = symbol_parts[0]
            content = self.aider_service.extract_symbol_content(symbol_name, file_path, self.project_path)
            if content:
                return file_path, symbol_name, content

        return None, None, None

    def process_context_request(self, request: ContextRequest) -> Dict[str, Any]:
        """
        Process a context request, extracting the requested symbols and their dependencies.

        Args:
            request: The context request to process

        Returns:
            A dictionary containing the extracted context
        """
        # Create a cache key for this request
        cache_key = f"context_request:{','.join([s.name for s in request.symbols_of_interest])}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
            "dependency_snippets": [],
            "not_found_symbols": []  # Track symbols that couldn't be found
        }

        # Process each requested symbol
        for symbol in request.symbols_of_interest:
            # Check if the symbol is referring to an installed Aider package (not local development)
            if symbol.name.startswith('aider.') and not (symbol.file_hint and ('aider-main' in symbol.file_hint or 'aider/' in symbol.file_hint)):
                print(f"Warning: Symbol {symbol.name} appears to be part of the installed Aider package, not your project.")
                result["not_found_symbols"].append({
                    "symbol_name": symbol.name,
                    "reason": "Symbol appears to be part of the installed Aider package, not your project."
                })
                continue

            file_path, symbol_name, content = self._extract_symbol_content(symbol)
            if not file_path or not symbol_name or not content:
                result["not_found_symbols"].append({
                    "symbol_name": symbol.name,
                    "reason": "Could not find or extract the symbol content."
                })
                continue

            try:
                # Extract essential imports
                try:
                    essential_imports = self.aider_service.extract_essential_imports(self.project_path, file_path)
                except Exception as e:
                    print(f"Warning: Could not extract essential imports for {symbol.name}: {e}")
                    essential_imports = ""

                # Extract containing class signature if it's a method
                containing_class = None
                if '.' in symbol.name:
                    try:
                        containing_class = self.aider_service.extract_containing_class(self.project_path, file_path, symbol_name)
                    except Exception as e:
                        print(f"Warning: Could not extract containing class for {symbol.name}: {e}")

                # Extract usage contexts (temporarily disabled to avoid scanning entire repository)
                # TODO: Re-enable with smarter file filtering to avoid reading non-existent files
                usage_contexts = []
                # try:
                #     usage_contexts = self.aider_service.extract_usage_contexts(self.project_path, symbol_name, file_path)
                # except Exception as e:
                #     print(f"Warning: Could not extract usage contexts for {symbol.name}: {e}")
                #     usage_contexts = []

                # Add the extracted symbol to the result
                result["extracted_symbols"].append({
                    "symbol_name": symbol.name,
                    "file_path": file_path,
                    "content": content,
                    "essential_imports": essential_imports,
                    "containing_class": containing_class
                })

                # Add dependency snippets
                for usage in usage_contexts[:3]:  # Limit to 3 usage examples
                    result["dependency_snippets"].append({
                        "file_path": usage.get("file_path", ""),
                        "symbol_name": usage.get("symbol_name", ""),
                        "content": usage.get("content", ""),
                        "usage_type": usage.get("usage_type", "unknown")
                    })
            except Exception as e:
                print(f"Error processing symbol {symbol.name}: {e}")
                result["not_found_symbols"].append({
                    "symbol_name": symbol.name,
                    "reason": f"Error processing symbol: {str(e)}"
                })

        # Cache the result
        self._update_cache(cache_key, result)

        return result

    def _create_llm_friendly_package(self, context_package: Dict[str, Any], request: IRContextRequest) -> str:
        """
        Create an LLM-friendly compact version of the context package.

        Args:
            context_package: The full context package
            request: The original IR context request

        Returns:
            Compact string representation suitable for LLM consumption
        """
        print(f"   🎯 Creating LLM-friendly package (target: <{request.max_output_chars:,} chars)")

        # Start with essential information
        user_query = context_package.get('user_query', '')
        task_description = context_package.get('task_description', '')
        task_type = context_package.get('task_type', '')

        # Get the most critical entities only
        ir_slices = context_package.get('ir_slices', [])
        code_context = context_package.get('code_context', [])

        # Filter out low-signal items first
        def is_low_signal_item(ir):
            """Check if an entity is a low-signal item that should be filtered out."""
            entity_name = ir.get('entity_name', '').lower()
            entity_type = ir.get('entity_type', '')
            file_path = ir.get('file_path', '').lower()

            # Filter out main() functions (CLI entry points) unless they're critical
            if entity_name == 'main' and entity_type == 'function':
                if ir.get('criticality', 'low') not in ['critical']:
                    return True

            # Filter out low-priority utility functions unless they're high criticality
            low_priority_functions = [
                'get_repo_map_tokens', 'token_count', 'validate_environment',
                'configure_model_settings', 'sanity_check_model',
                'try_to_select_default_model', 'select_default_model'
            ]

            if (entity_name in low_priority_functions and
                ir.get('criticality', 'low') not in ['high', 'critical']):
                return True

            # ENHANCED: Filter out debug/test artifacts and variables
            debug_artifacts = [
                'function_lines', 'found_functions', 'similar_functions', 'ir_file', 'data',
                'module_name', 'func_name', 'details', 'success', 'project_path', 'handler',
                'test_query', 'request', 'result', 'package', 'lines', 'relevant_lines',
                'start', 'end', 'count', 'aider_main_path'
            ]

            if entity_name in debug_artifacts and entity_type in ['variable', 'constant']:
                return True

            # Filter out single-letter variables unless they're in mathematical contexts
            if (len(entity_name) == 1 and entity_type == 'variable' and
                'math' not in file_path.lower() and 'calculation' not in file_path.lower()):
                return True

            # Filter out test functions unless they're specifically relevant to the query
            if (entity_name.startswith('test_') and
                entity_type == 'function' and
                ir.get('criticality', 'low') not in ['high', 'critical']):
                # Keep test functions only if they're directly related to the query focus
                user_query = context_package.get('user_query', '').lower()
                if not any(keyword in user_query for keyword in ['test', 'testing', entity_name]):
                    return True

            # Filter out debug functions unless they're specifically relevant
            if (entity_name.startswith('debug_') or entity_name.startswith('find_') and
                'debug' in file_path.lower() and
                ir.get('criticality', 'low') not in ['high', 'critical']):
                user_query = context_package.get('user_query', '').lower()
                if not any(keyword in user_query for keyword in ['debug', 'find', entity_name]):
                    return True

            return False

        # Apply low-signal filtering
        filtered_ir_slices = [ir for ir in ir_slices if not is_low_signal_item(ir)]

        print(f"   🔧 Filtered out {len(ir_slices) - len(filtered_ir_slices)} low-signal items")

        # ENHANCED: Dynamic result count - only show highly relevant results
        # Sort by relevance score first, not by criticality
        sorted_ir_slices = sorted(filtered_ir_slices, key=lambda x: x.get('relevance_score', 0), reverse=True)

        # Define relevance thresholds for quality filtering
        HIGH_RELEVANCE_THRESHOLD = 8.0  # Only include entities with score >= 8.0 for high quality
        MEDIUM_RELEVANCE_THRESHOLD = 4.0  # Include medium relevance if needed
        MINIMUM_RESULTS = 1  # Always include at least 1 result (the main function)
        MAXIMUM_RESULTS = 3  # Cap at 3 entities for focused results

        # Filter by relevance score for quality
        highly_relevant = [ir for ir in sorted_ir_slices if ir.get('relevance_score', 0) >= HIGH_RELEVANCE_THRESHOLD]
        medium_relevant = [ir for ir in sorted_ir_slices if ir.get('relevance_score', 0) >= MEDIUM_RELEVANCE_THRESHOLD]

        if len(highly_relevant) >= MINIMUM_RESULTS:
            # Use only highly relevant results
            critical_ir = highly_relevant[:MAXIMUM_RESULTS]
            print(f"   🎯 Selected {len(critical_ir)} highly relevant entities (score >= {HIGH_RELEVANCE_THRESHOLD}):")
        elif len(medium_relevant) >= MINIMUM_RESULTS:
            # Use medium relevant results
            critical_ir = medium_relevant[:MAXIMUM_RESULTS]
            print(f"   🎯 Selected {len(critical_ir)} medium relevant entities (score >= {MEDIUM_RELEVANCE_THRESHOLD}):")
        else:
            # Fallback: ensure minimum results even if relevance is low
            critical_ir = sorted_ir_slices[:max(MINIMUM_RESULTS, len(highly_relevant))]
            print(f"   🎯 Selected {len(critical_ir)} entities (including {len(highly_relevant)} highly relevant):")

        for i, ir in enumerate(critical_ir[:8], 1):  # Show all selected
            score = ir.get('relevance_score', 0)
            relevance_indicator = "🔥" if score >= HIGH_RELEVANCE_THRESHOLD else "⚡" if score >= 3.0 else "📝"
            print(f"      {i}. {relevance_indicator} {ir.get('entity_name', 'unknown')} (score: {score:.2f}, criticality: {ir.get('criticality', 'unknown')})")

        # Get corresponding code implementations with enhanced matching
        print(f"   🔍 Matching code implementations for {len(critical_ir)} critical entities...")

        # Debug: Show what critical entities we have
        critical_entity_names = [ir['entity_name'] for ir in critical_ir]
        print(f"   📋 Critical entities: {critical_entity_names}")

        # Debug: Show what code context we have
        code_entity_names = [code['entity_name'] for code in code_context]
        print(f"   📋 Available code entities: {code_entity_names[:10]}...")  # Show first 10

        # Enhanced matching logic - try multiple matching strategies
        critical_code = []
        matched_entities = set()
        seen_entities = set()  # Prevent duplicates

        # CONSISTENCY FIX: Track which entities we actually find implementations for
        # This will be used to update the critical_ir list to match the implementations
        entities_with_implementations = []

        for ir in critical_ir:
            ir_entity_name = ir['entity_name']

            # Skip if we've already included this entity
            if ir_entity_name in seen_entities:
                continue

            # Strategy 1: Exact match
            exact_matches = [code for code in code_context if code['entity_name'] == ir_entity_name]
            if exact_matches:
                # Only take the first match to avoid duplicates
                critical_code.append(exact_matches[0])
                matched_entities.add(ir_entity_name)
                seen_entities.add(ir_entity_name)
                # Track that this IR entity has an implementation
                entities_with_implementations.append(ir)
                print(f"   ✅ EXACT MATCH: Found code for {ir_entity_name}")
                continue

            # Strategy 2: Partial match (for methods that might have class prefixes)
            partial_matches = [code for code in code_context
                             if ir_entity_name in code['entity_name'] or code['entity_name'] in ir_entity_name]
            if partial_matches:
                # Only take the first match to avoid duplicates
                for match in partial_matches:
                    if match['entity_name'] not in seen_entities:
                        critical_code.append(match)
                        seen_entities.add(match['entity_name'])
                        break
                matched_entities.add(ir_entity_name)
                # Track that this IR entity has an implementation
                entities_with_implementations.append(ir)
                print(f"   ⚡ PARTIAL MATCH: Found code for {ir_entity_name} -> {[c['entity_name'] for c in partial_matches[:1]]}")
                continue

            print(f"   ❌ NO MATCH: Could not find code for {ir_entity_name}")

        # Limit to max_entities
        critical_code = critical_code[:request.max_entities]

        print(f"   📊 Code matching results: {len(critical_code)} implementations found for {len(matched_entities)} entities")

        # ENHANCED CONSISTENCY FIX: Create IR entries that match the actual code implementations
        # This ensures CRITICAL ENTITIES section perfectly matches KEY IMPLEMENTATIONS section
        if critical_code:
            print(f"   🔄 ENHANCED CONSISTENCY FIX: Rebuilding critical_ir to match actual implementations")

            # Create new IR entries based on the actual code implementations found
            new_critical_ir = []
            for code_impl in critical_code:
                entity_name = code_impl['entity_name']

                # Try to find the original IR entry for this entity
                original_ir = None
                for ir in critical_ir:
                    if ir['entity_name'] == entity_name:
                        original_ir = ir
                        break

                # If we found the original IR, use it; otherwise create a new one
                if original_ir:
                    new_critical_ir.append(original_ir)
                    print(f"      ✅ Kept original IR for: {entity_name}")
                else:
                    # Create a new IR entry based on the code implementation
                    # Try to find any IR entry that might be related (for metadata)
                    related_ir = None
                    for ir in critical_ir:
                        if (entity_name.lower() in ir['entity_name'].lower() or
                            ir['entity_name'].lower() in entity_name.lower()):
                            related_ir = ir
                            break

                    # Create new IR entry
                    new_ir_entry = {
                        'entity_name': entity_name,
                        'entity_type': 'function',  # Most implementations are functions
                        'file_path': code_impl.get('file_path', 'unknown'),
                        'criticality': related_ir.get('criticality', 'medium') if related_ir else 'medium',
                        'change_risk': related_ir.get('change_risk', 'medium') if related_ir else 'medium',
                        'relevance_score': code_impl.get('relevance_score', 8.0),
                        'calls': related_ir.get('calls', []) if related_ir else [],
                        'used_by': related_ir.get('used_by', []) if related_ir else [],
                        'side_effects': related_ir.get('side_effects', []) if related_ir else [],
                        'class_name': related_ir.get('class_name') if related_ir else None,
                        'inherits_from': related_ir.get('inherits_from', []) if related_ir else [],
                        'method_overrides': related_ir.get('method_overrides', []) if related_ir else [],
                        'calls_super': related_ir.get('calls_super', False) if related_ir else False,
                        'overridden_by': related_ir.get('overridden_by', []) if related_ir else []
                    }
                    new_critical_ir.append(new_ir_entry)
                    print(f"      🆕 Created new IR for: {entity_name}")

            original_count = len(critical_ir)
            critical_ir = new_critical_ir
            print(f"   ✅ CONSISTENCY SUCCESS: Updated critical_ir from {original_count} to {len(critical_ir)} entities")
            print(f"      📋 Final entities: {[ir['entity_name'] for ir in critical_ir]}")
        else:
            print(f"   ⚠️  WARNING: No code implementations found - keeping original IR selection")

        # Build clean package - just the context the LLM requested
        compact_content = f"""## CRITICAL ENTITIES ({len(critical_ir)} most important)
"""

        # Add compact IR data with inheritance information
        for i, ir in enumerate(critical_ir, 1):
            # Get actual function names (limit to first 5 for readability)
            all_calls = ir.get('calls', [])
            all_used_by = ir.get('used_by', [])

            calls = all_calls[:5]
            used_by = all_used_by[:5]

            calls_str = f"[{', '.join(f'"{call}"' for call in calls)}]" if calls else "[]"
            used_by_str = f"[{', '.join(f'"{user}"' for user in used_by)}]" if used_by else "[]"

            # Add "..." if there are more items, and include total count
            total_calls = len(all_calls)
            total_used_by = len(all_used_by)

            if total_calls > 5:
                calls_str = calls_str[:-1] + ', "..."]'
            calls_str += f" (total: {total_calls})" if total_calls > 0 else ""

            if total_used_by > 5:
                used_by_str = used_by_str[:-1] + ', "..."]'
            used_by_str += f" (total: {total_used_by})" if total_used_by > 0 else ""

            # Get entity information
            entity_type = ir.get('entity_type', 'function')
            entity_name = ir.get('entity_name', 'unknown')
            file_path = ir.get('file_path', '')

            # ENHANCED: Use actual class information from IR data
            actual_class_name = ir.get('class_name', None)
            is_method = actual_class_name is not None

            # Legacy detection for backward compatibility
            is_likely_method = False
            potential_class_name = ""

            # Check if the entity name suggests it's a method (contains class-like patterns)
            if '.' in entity_name:
                parts = entity_name.split('.')
                if len(parts) == 2:
                    potential_class_name = parts[0]
                    method_name = parts[1]
                    is_likely_method = True

            # Also check if the file suggests it's in a class-based structure
            if not is_likely_method and not is_method and any(keyword in file_path.lower() for keyword in ['coder', 'base_', 'handler', 'service']):
                # These patterns often indicate class-based code
                if entity_type == 'function':
                    # Check if function name suggests it's a method
                    if any(pattern in entity_name for pattern in ['process_', 'handle_', 'get_', 'set_', 'create_', 'update_']):
                        is_likely_method = True
                        # Try to infer class name from file name
                        import os
                        base_name = os.path.basename(file_path).replace('.py', '')
                        potential_class_name = ''.join(word.capitalize() for word in base_name.split('_'))

            # Build the entity header with enhanced context using actual IR data
            if is_method or (is_likely_method and potential_class_name):
                # This is a method - use actual class name from IR
                class_name_to_use = actual_class_name or potential_class_name
                entity_header = f"### {i}. {entity_name} (method)"
                class_info = f"- **Belongs to Class**: `{class_name_to_use}`"

                # Add inheritance information from IR
                inherits_from = ir.get('inherits_from', [])
                if inherits_from:
                    inheritance_info = f"- **Inherits From**: {' → '.join(inherits_from)}"
                else:
                    inheritance_info = f"- **Inherits From**: No inheritance (base class)"

                # Add method context section with actual inheritance data
                override_section = f"\n\n#### 🔁 Class Context"
                override_section += f"\n- Part of `{class_name_to_use}` class"

                if inherits_from:
                    override_section += f"\n- **Inheritance chain**: {' → '.join(inherits_from)}"

                # Add override information
                method_overrides = ir.get('method_overrides', [])
                if method_overrides:
                    override_section += f"\n- **Overrides**: {', '.join(method_overrides)}"

                overridden_by = ir.get('overridden_by', [])
                if overridden_by:
                    override_section += f"\n- **Overridden by**: {', '.join(overridden_by)}"

                # Build method details section
                method_details = f"\n\n#### 🧩 Method Details"

                # Add super() call information
                calls_super = ir.get('calls_super', False)
                if calls_super:
                    method_details += f"\n- **Calls super()**: Yes"
                else:
                    method_details += f"\n- **Calls super()**: No"

            else:
                # For functions, classes, or other entities
                entity_header = f"### {i}. {entity_name} ({entity_type})"
                class_info = ""
                override_section = ""
                method_details = ""

                # Add inheritance information for classes
                if entity_type == "class":
                    inherits_from = ir.get('inherits_from', [])
                    if inherits_from:
                        inheritance_info = f"- **Inherits From**: {' → '.join(inherits_from)}"
                        # Add class inheritance context
                        override_section = f"\n\n#### 🔁 Class Inheritance"
                        override_section += f"\n- **Inheritance chain**: {' → '.join(inherits_from)}"

                        # Add override information for classes
                        method_overrides = ir.get('method_overrides', [])
                        if method_overrides:
                            override_section += f"\n- **Methods that override parent**: {', '.join(method_overrides)}"

                        overridden_by = ir.get('overridden_by', [])
                        if overridden_by:
                            override_section += f"\n- **Methods overridden by children**: {', '.join(overridden_by)}"
                    else:
                        inheritance_info = f"- **Inherits From**: No inheritance (base class)"
                else:
                    inheritance_info = ""

            compact_content += f"""
{entity_header}
- File: {ir['file_path']}
{class_info}
{inheritance_info}
- Criticality: {ir['criticality']} | Risk: {ir['change_risk']}{override_section}{method_details}
- **Calls**: {calls_str}
- **Used by**: {used_by_str}
- **Side Effects**: {', '.join(ir.get('side_effects', [])[:3]) if ir.get('side_effects') else 'None'}
"""

        # Add essential source code (truncated) with context selection notice
        if critical_code:
            compact_content += f"""
⚠️ **Context Selection Notice**
The following code context was selected based on the user's query and initial relevance scoring.
It is not guaranteed to be the most relevant or complete subset of the codebase.

You, as the AI assistant, are responsible for validating assumptions, identifying potential missing dependencies, and requesting further context if needed.

A full index of related modules and functions is available below for reference.

## KEY IMPLEMENTATIONS ({len(critical_code)} functions)
Complete code available on request for any function.
"""

            for i, code in enumerate(critical_code, 1):
                # Show more implementation for better context
                source = code['source_code']
                entity_name = code['entity_name']

                # For the main function being asked about, show more code
                user_query_lower = user_query.lower()
                entity_name_lower = entity_name.lower()

                # Enhanced main focus detection
                is_main_focus = False

                # Check if entity name is directly mentioned
                if entity_name_lower in user_query_lower:
                    is_main_focus = True

                # Check for key terms that indicate this is the main function
                key_terms = ['calculate', 'position', 'quantity', 'calculator']
                entity_matches = sum(1 for term in key_terms if term in entity_name_lower)
                query_matches = sum(1 for term in key_terms if term in user_query_lower)

                # If entity has multiple key terms from the query, it's likely the main focus
                if entity_matches >= 2 and query_matches >= 2:
                    is_main_focus = True

                # Special case: if this is a calculation function and query asks about calculation
                if ('calculate' in entity_name_lower and 'calculator' in user_query_lower) or \
                   ('calculator' in entity_name_lower and 'calculate' in user_query_lower):
                    is_main_focus = True

                # Debug output to see what's happening
                print(f"   🔍 Focus detection for '{entity_name}':")
                print(f"      Entity matches: {entity_matches}, Query matches: {query_matches}")
                print(f"      Is main focus: {is_main_focus}")

                if is_main_focus:
                    char_limit = 8000  # Show much more for the main function being asked about
                    print(f"      📏 Using extended char limit: {char_limit}")
                else:
                    char_limit = 1500  # Show more for supporting functions
                    print(f"      📏 Using standard char limit: {char_limit}")

                if len(source) > char_limit:
                    lines = source.split('\n')
                    truncated_lines = []
                    char_count = 0
                    for line in lines:
                        if char_count + len(line) > char_limit:
                            truncated_lines.append("    # ... (implementation continues)")
                            break
                        truncated_lines.append(line)
                        char_count += len(line)
                    source = '\n'.join(truncated_lines)

                compact_content += f"""
### {i}. {code['entity_name']}
```python
{source}
```
"""

        # Add Tier 2: Awareness Index (Lower-Score Context)
        compact_content += self._generate_awareness_index(context_package, critical_ir)

        # Check final size and reduce further if needed
        final_size = len(compact_content)
        print(f"   📏 Compact package: {final_size:,} characters")

        if final_size > request.max_output_chars:
            print(f"   ⚠️  Still too large! Reducing further...")
            # Further reduction: fewer entities and shorter code snippets
            critical_ir = critical_ir[:max(1, request.max_entities // 2)]
            critical_code = critical_code[:max(1, request.max_entities // 2)]
            # Rebuild with smaller limits (implementation would go here)

        return compact_content

    def _generate_awareness_index(self, context_package: Dict[str, Any], critical_ir: List[Dict]) -> str:
        """
        Generate Tier 2: Awareness Index with lower-score context entities.

        Args:
            context_package: The full context package with all IR slices
            critical_ir: List of critical IR entities already included in Tier 1

        Returns:
            Formatted awareness index string
        """
        print(f"   🗂️  Generating Awareness Index (Tier 2 context)...")

        # Get all IR slices from the context package
        all_ir_slices = context_package.get('ir_slices', [])

        # Get entity names that are already in Tier 1 (critical entities)
        critical_entity_names = {ir['entity_name'] for ir in critical_ir}

        # Filter out critical entities to get lower-score context
        awareness_entities = [
            ir for ir in all_ir_slices
            if ir['entity_name'] not in critical_entity_names
        ]

        # Sort by relevance score (descending) to show most relevant first
        awareness_entities.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

        # Limit to reasonable number for awareness (don't overwhelm)
        max_awareness_entities = 15  # Show up to 15 additional entities
        awareness_entities = awareness_entities[:max_awareness_entities]

        if not awareness_entities:
            return "\n## AWARENESS INDEX\nNo additional entities available for awareness context.\n"

        print(f"   📋 Including {len(awareness_entities)} entities in awareness index")

        # Group entities by file for better organization
        entities_by_file = {}
        for entity in awareness_entities:
            file_path = entity.get('file_path', 'unknown')
            if file_path not in entities_by_file:
                entities_by_file[file_path] = []
            entities_by_file[file_path].append(entity)

        # Build awareness index content
        awareness_content = f"""
## AWARENESS INDEX ({len(awareness_entities)} additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

"""

        # Add entities grouped by file
        for file_path, file_entities in entities_by_file.items():
            # Shorten file path for readability
            display_path = file_path.replace('\\', '/').split('/')[-1] if '/' in file_path or '\\' in file_path else file_path

            # Group entities by type within each file
            functions = [e for e in file_entities if e.get('entity_type') == 'function']
            classes = [e for e in file_entities if e.get('entity_type') == 'class']
            others = [e for e in file_entities if e.get('entity_type') not in ['function', 'class']]

            awareness_content += f"### 📁 {display_path}\n"

            if functions:
                func_names = [f['entity_name'] for f in functions[:8]]  # Limit to 8 per file
                if len(functions) > 8:
                    func_names.append(f"... (+{len(functions) - 8} more)")
                awareness_content += f"- **Functions**: {', '.join(func_names)}\n"

            if classes:
                class_names = [c['entity_name'] for c in classes[:5]]  # Limit to 5 classes per file
                if len(classes) > 5:
                    class_names.append(f"... (+{len(classes) - 5} more)")
                awareness_content += f"- **Classes**: {', '.join(class_names)}\n"

            if others:
                other_names = [o['entity_name'] for o in others[:5]]  # Limit to 5 others per file
                if len(others) > 5:
                    other_names.append(f"... (+{len(others) - 5} more)")
                awareness_content += f"- **Other**: {', '.join(other_names)}\n"

            awareness_content += "\n"

        # Add summary statistics
        total_functions = sum(1 for e in awareness_entities if e.get('entity_type') == 'function')
        total_classes = sum(1 for e in awareness_entities if e.get('entity_type') == 'class')
        total_files = len(entities_by_file)

        awareness_content += f"""**Summary**: {total_functions} functions, {total_classes} classes across {total_files} files
*To request specific implementations, use: "IR_REQUEST"*

"""

        return awareness_content

    def process_ir_context_request(self, request: IRContextRequest) -> Dict[str, Any]:
        """
        Process an intelligent context request using IR and ICD.

        Args:
            request: The IR context request to process

        Returns:
            A dictionary containing the intelligent context bundle
        """
        print(f"🧠 Processing IR Context Request: {request.task_description}")

        # Create a cache key for this request
        cache_key = f"ir_context_request:{hash(request.user_query + request.task_description)}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            print("📋 Using cached IR context result")
            return cached_result

        try:
            # Try to get IR data from cache first
            print("🔍 Loading IR data...")
            ir_data = self._get_ir_from_cache(self.project_path)

            # Force fresh IR generation for debugging compute_next_boundary issue
            # TODO: Remove this after debugging is complete
            if ir_data is not None:
                print("🔄 Forcing fresh IR generation (cache invalidated for debugging)")
                ir_data = None

            if ir_data is None:
                # Generate IR data if not cached
                print("🔄 Generating new IR data...")

                # Import the standalone AiderIntegrationService that has IR generation
                import sys
                import os

                # Add the root directory to path to import the standalone service
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                if root_dir not in sys.path:
                    sys.path.insert(0, root_dir)

                try:
                    from aider_integration_service import AiderIntegrationService as StandaloneService
                    standalone_service = StandaloneService()

                    start_time = time.time()
                    ir_data = standalone_service.generate_mid_level_ir(self.project_path)
                    generation_time = time.time() - start_time

                    # Cache the generated data
                    self._update_ir_cache(self.project_path, ir_data)

                    # Save IR data to project directory for debugging
                    self._save_ir_to_project_directory(ir_data)

                    print(f"✅ IR generation completed in {generation_time:.2f} seconds")

                except ImportError:
                    # Fallback: try to use the existing service if it has the method
                    if hasattr(self.aider_service, 'generate_mid_level_ir'):
                        start_time = time.time()
                        ir_data = self.aider_service.generate_mid_level_ir(self.project_path)
                        generation_time = time.time() - start_time

                        # Cache the generated data
                        self._update_ir_cache(self.project_path, ir_data)

                        # Save IR data to project directory for debugging
                        self._save_ir_to_project_directory(ir_data)

                        print(f"✅ IR generation completed in {generation_time:.2f} seconds")
                    else:
                        raise Exception("IR generation not available. Please ensure aider_integration_service.py is accessible.")

            # Import and use the NEW Hierarchical Context Selector
            from .hierarchical_context_selector import HierarchicalContextSelector
            from .semantic_context_integration import SemanticContextIntegration

            print("🏗️ Using Hierarchical Architectural Context Selection...")

            # Create hierarchical context selector
            hierarchical_selector = HierarchicalContextSelector()

            # Use hierarchical selection based on system architecture
            hierarchical_context = hierarchical_selector.select_hierarchical_context(
                ir_data=ir_data,
                user_query=request.user_query,
                focus_entities=request.focus_entities,
                max_entities=request.max_entities
            )

            # Also create semantic integration for LLM package generation
            semantic_integration = SemanticContextIntegration()

            # Convert hierarchical context to semantic format for compatibility
            enhanced_context = self._convert_hierarchical_to_semantic_context(
                hierarchical_context, ir_data, request
            )

            # Convert enhanced context to the expected format for backward compatibility
            context_bundle = self._convert_semantic_to_context_bundle(enhanced_context)

            # Debug: Check if conversion worked
            print(f"🔍 Context bundle conversion result:")
            print(f"   Enhanced context ir_slices: {len(enhanced_context.get('ir_slices', []))}")
            print(f"   Context bundle entities: {len(context_bundle.entities)}")
            if len(context_bundle.entities) == 0 and len(enhanced_context.get('ir_slices', [])) > 0:
                print(f"   ⚠️  Warning: Conversion lost entities!")
                # Show first few ir_slices for debugging
                for i, ir_slice in enumerate(enhanced_context.get('ir_slices', [])[:3]):
                    print(f"      IR slice {i+1}: {ir_slice.get('entity_name', 'unknown')}")
            else:
                print(f"   ✅ Conversion successful")

            # Build the response
            result = {
                "user_query": request.user_query,
                "task_description": request.task_description,
                "task_type": request.task_type,
                "context_bundle": {
                    "total_entities": len(context_bundle.entities),
                    "total_tokens": context_bundle.total_tokens,
                    "selection_rationale": context_bundle.selection_rationale,
                    "entities": []
                }
            }

            # Add IR slices if requested
            if request.include_ir_slices:
                result["ir_slices"] = []
                for entity in context_bundle.entities:
                    ir_slice = {
                        "module_name": entity.module_name,
                        "entity_name": entity.entity_name,
                        "entity_type": entity.entity_type,
                        "file_path": entity.file_path,
                        "criticality": entity.criticality,
                        "change_risk": entity.change_risk,
                        "relevance_score": entity.relevance_score,
                        "priority": entity.priority.value,
                        "calls": entity.calls,
                        "used_by": entity.used_by,
                        "side_effects": entity.side_effects,
                        "errors": entity.errors,
                        # Add inheritance data from enhanced IR
                        "class_name": getattr(entity, 'class_name', None),
                        "inherits_from": getattr(entity, 'inherits_from', []),
                        "method_overrides": getattr(entity, 'method_overrides', []),
                        "calls_super": getattr(entity, 'calls_super', False),
                        "overridden_by": getattr(entity, 'overridden_by', [])
                    }
                    result["ir_slices"].append(ir_slice)

            # Add code context if requested
            if request.include_code_context:
                result["code_context"] = []
                for entity in context_bundle.entities:
                    try:
                        # Extract the actual source code for this entity
                        symbol_name = entity.entity_name
                        if entity.entity_type == "function" and "." not in symbol_name:
                            # Try to get the containing class if it's a method
                            containing_class = self.aider_service.extract_containing_class(
                                self.project_path, entity.file_path, symbol_name
                            )
                            if containing_class:
                                symbol_name = f"{containing_class}.{symbol_name}"

                        content = self.aider_service.extract_symbol_content(
                            symbol_name, entity.file_path, self.project_path
                        )

                        if content:
                            code_context = {
                                "entity_name": entity.entity_name,
                                "file_path": entity.file_path,
                                "priority": entity.priority.value,
                                "relevance_score": entity.relevance_score,
                                "source_code": content
                            }
                            result["code_context"].append(code_context)
                    except Exception as e:
                        print(f"Warning: Could not extract code for {entity.entity_name}: {e}")
                        continue

            # Add summary statistics
            result["summary"] = {
                "critical_entities": len([e for e in context_bundle.entities if e.priority.value == "critical"]),
                "high_priority_entities": len([e for e in context_bundle.entities if e.priority.value == "high"]),
                "files_involved": len(set(e.file_path for e in context_bundle.entities)),
                "token_utilization": f"{(context_bundle.total_tokens / request.max_tokens) * 100:.1f}%"
            }

            print(f"✅ IR Context processing complete:")
            print(f"   Selected {len(context_bundle.entities)} entities")
            print(f"   Token utilization: {result['summary']['token_utilization']}")
            print(f"   Critical entities: {result['summary']['critical_entities']}")

            # Generate LLM-friendly package if requested
            if request.llm_friendly:
                print(f"🤖 Generating SEMANTIC LLM-friendly package...")
                # Use semantic integration to create enhanced LLM package
                llm_package = semantic_integration.create_enhanced_llm_package(
                    enhanced_context, max_chars=request.max_output_chars
                )
                result["llm_friendly_package"] = llm_package
                result["package_size_chars"] = len(llm_package)

                # Check LLM compatibility
                if len(llm_package) <= 32000:
                    result["llm_compatibility"] = "GPT-4 compatible"
                elif len(llm_package) <= 100000:
                    result["llm_compatibility"] = "GPT-4 Turbo or Claude required"
                else:
                    result["llm_compatibility"] = "Large context LLM required"

                print(f"   📦 Semantic LLM package size: {len(llm_package):,} characters")
                print(f"   🤖 Compatibility: {result['llm_compatibility']}")
                print(f"   🧠 Enhanced with semantic explanations and architectural context")

                # Automatically save LLM package to ICA_package folder
                self._auto_save_llm_package(llm_package, request)

            # Cache the result
            self._update_cache(cache_key, result)

            return result

        except Exception as e:
            print(f"❌ Error processing IR context request: {e}")
            return {
                "error": f"Failed to process IR context request: {str(e)}",
                "user_query": request.user_query,
                "task_description": request.task_description
            }

    def _auto_save_llm_package(self, llm_package: str, request: IRContextRequest) -> None:
        """
        Automatically save the LLM-friendly package to the ICA_package folder.

        Args:
            llm_package: The LLM-friendly package content to save
            request: The original IR context request for metadata
        """
        try:
            import os
            import re
            from datetime import datetime
            from pathlib import Path

            # Create ICA_package folder if it doesn't exist
            ica_folder = Path(self.project_path) / "ICA_package"
            ica_folder.mkdir(exist_ok=True)

            # Generate timestamp in YYYYMMDD_HHMMSS format
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Sanitize task description for filename
            task_desc = request.task_description or request.user_query or "context_request"
            # Remove special characters and replace spaces with underscores
            sanitized_desc = re.sub(r'[^\w\s-]', '', task_desc)
            sanitized_desc = re.sub(r'[-\s]+', '_', sanitized_desc)
            # Limit length to avoid overly long filenames
            sanitized_desc = sanitized_desc[:50]
            # Remove trailing underscores
            sanitized_desc = sanitized_desc.strip('_').lower()

            # Generate filename
            filename = f"{timestamp}_{sanitized_desc}.txt"
            filepath = ica_folder / filename

            # Create file content with metadata header
            file_content = f"""# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Project: {self.project_path}
# User Query: {request.user_query}
# Task Description: {request.task_description}
# Task Type: {request.task_type}
# Max Tokens: {request.max_tokens}
# Focus Entities: {', '.join(request.focus_entities) if request.focus_entities else 'None'}
# Package Size: {len(llm_package):,} characters

{'='*80}

{llm_package}
"""

            # Save the file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(file_content)

            print(f"   💾 Auto-saved LLM package: {filename}")
            print(f"   📁 Location: {filepath}")

        except Exception as e:
            print(f"   ⚠️  Warning: Failed to auto-save LLM package: {e}")
            # Don't raise the exception - auto-saving is optional functionality

    def _save_ir_to_project_directory(self, ir_data: dict) -> None:
        """
        Save the IR data as JSON to the project directory for debugging and inspection.

        Args:
            ir_data: The IR data dictionary to save
        """
        try:
            import json
            import os
            from datetime import datetime
            from pathlib import Path

            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ir_data_{timestamp}.json"
            filepath = Path(self.project_path) / filename

            # Save the IR data
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(ir_data, f, indent=2, ensure_ascii=False)

            # Quick analysis for debugging
            modules = ir_data.get('modules', [])
            total_functions = sum(len(module.get('functions', [])) for module in modules)

            print(f"   💾 Saved IR data: {filename}")
            print(f"   📊 IR contains: {len(modules)} modules, {total_functions} functions")

            # Check for compute_next_boundary specifically
            found_compute_boundary = []
            for module in modules:
                functions = module.get('functions', [])
                for func in functions:
                    func_name = func.get('name', '')
                    if 'compute_next_boundary' in func_name.lower():
                        found_compute_boundary.append({
                            'name': func_name,
                            'file': module.get('file'),
                            'line': func.get('line_start')
                        })

            if found_compute_boundary:
                print(f"   ✅ Found compute_next_boundary: {len(found_compute_boundary)} instances")
                for func in found_compute_boundary:
                    print(f"      - {func['name']} in {func['file']}:{func['line']}")
            else:
                print(f"   ❌ compute_next_boundary NOT found in IR data")

        except Exception as e:
            print(f"   ⚠️  Warning: Failed to save IR data: {e}")
            # Don't raise the exception - saving is optional functionality

    def _convert_semantic_to_context_bundle(self, enhanced_context: Dict[str, Any]):
        """
        Convert semantic context to the expected context bundle format for backward compatibility.

        Args:
            enhanced_context: Enhanced context from semantic integration

        Returns:
            Context bundle compatible with existing system
        """
        from .intelligent_context_selector import ContextEntity, ContextPriority, ContextBundle

        ir_slices = enhanced_context.get('ir_slices', [])
        entities = []

        for ir_slice in ir_slices:
            # Map semantic criticality to priority
            criticality = ir_slice.get('criticality', 'medium')
            if criticality == 'critical':
                priority = ContextPriority.CRITICAL
            elif criticality == 'high':
                priority = ContextPriority.HIGH
            elif criticality == 'medium':
                priority = ContextPriority.MEDIUM
            else:
                priority = ContextPriority.LOW

            # Create ContextEntity
            entity = ContextEntity(
                module_name=ir_slice.get('module_name', ''),
                entity_name=ir_slice.get('entity_name', ''),
                entity_type=ir_slice.get('entity_type', 'function'),
                file_path=ir_slice.get('file_path', ''),
                criticality=ir_slice.get('criticality', 'medium'),
                change_risk=ir_slice.get('change_risk', 'medium'),
                relevance_score=ir_slice.get('relevance_score', 0.0),
                priority=priority,
                calls=ir_slice.get('calls', []),
                used_by=ir_slice.get('used_by', []),
                side_effects=ir_slice.get('side_effects', []),
                errors=[]
            )
            entities.append(entity)

        # Create semantic selection rationale
        semantic_analysis = enhanced_context.get('semantic_analysis', {})
        query_analysis = semantic_analysis.get('query_analysis')

        if query_analysis:
            selection_rationale = f"""🧠 SEMANTIC INTELLIGENCE SELECTION

Query Intent: {query_analysis.intent.value}
Query Scope: {query_analysis.scope.value}
Analysis Confidence: {query_analysis.confidence:.2f}

Domain Concepts Identified:
"""
            for concept in query_analysis.domain_concepts:
                selection_rationale += f"- {concept.category.title()}: {concept.concept} (confidence: {concept.confidence:.2f})\n"

            selection_rationale += f"""
Selection Method: Intelligent Semantic Analysis (not keyword matching)
Components Selected: {len(entities)}
Selection Confidence: {semantic_analysis.get('selection_confidence', 0.0):.2f}

This selection uses semantic understanding of component purposes and query intent,
providing more relevant context than traditional keyword-based approaches."""
        else:
            selection_rationale = "Semantic analysis completed"

        # Estimate tokens (rough approximation)
        total_tokens = len(entities) * 100  # Rough estimate

        # Import TaskType for the context bundle
        from .intelligent_context_selector import TaskType

        return ContextBundle(
            task_description=enhanced_context.get('task_description', 'Semantic analysis'),
            task_type=TaskType.GENERAL_ANALYSIS,  # Default task type
            entities=entities,
            total_tokens=total_tokens,
            selection_rationale=selection_rationale
        )

    def _convert_hierarchical_to_semantic_context(self, hierarchical_context: Dict[str, Any],
                                                 ir_data: Dict[str, Any],
                                                 request) -> Dict[str, Any]:
        """
        Convert hierarchical context to semantic format for compatibility.

        Args:
            hierarchical_context: Context from hierarchical selector
            ir_data: Original IR data
            request: Original request

        Returns:
            Enhanced context in semantic format
        """
        selected_entities = hierarchical_context.get('selected_entities', [])
        architectural_explanation = hierarchical_context.get('architectural_explanation', '')
        query_analysis = hierarchical_context.get('query_analysis')

        # Convert selected entities to IR slices format
        ir_slices = []
        for entity in selected_entities:
            ir_slice = {
                'entity_name': entity.get('name', ''),
                'entity_type': entity.get('type', 'function'),
                'module_name': entity.get('module_name', ''),
                'file_path': entity.get('file_path', ''),
                'criticality': entity.get('criticality', 'medium'),
                'change_risk': entity.get('change_risk', 'medium'),
                'relevance_score': entity.get('relevance_score', 0.0),
                'calls': entity.get('calls', []),
                'used_by': entity.get('used_by', []),
                'side_effects': entity.get('side_effects', []),
                'cluster': entity.get('cluster', ''),
                'semantic_explanation': f"Selected from {entity.get('cluster', 'unknown')} cluster. {architectural_explanation[:100]}..."
            }
            ir_slices.append(ir_slice)

        # Create semantic analysis structure
        semantic_analysis = {
            'query_analysis': query_analysis,
            'selection_confidence': 0.85,  # High confidence for architectural selection
            'architectural_context': hierarchical_context.get('architectural_context'),
            'selection_method': 'hierarchical_architectural'
        }

        # Create enhanced context
        enhanced_context = {
            'ir_slices': ir_slices,
            'semantic_analysis': semantic_analysis,
            'task_description': request.task_description,
            'user_query': request.user_query,
            'architectural_explanation': architectural_explanation,
            'system_architecture': hierarchical_context.get('system_architecture')
        }

        return enhanced_context
