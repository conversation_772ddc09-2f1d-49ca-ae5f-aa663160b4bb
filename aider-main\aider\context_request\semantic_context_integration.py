"""
Semantic Context Integration
Integrates the intelligent semantic selector with the existing IR Context System.
"""

import sys
import os
from typing import List, Dict, Optional, Any

from .intelligent_semantic_selector import IntelligentSemanticSelector
from .intelligent_context_models import ComponentRelevanceScore, QueryContext


class SemanticContextIntegration:
    """
    Integration layer that replaces keyword matching with semantic understanding
    in the existing IR Context System.
    """
    
    def __init__(self):
        self.semantic_selector = IntelligentSemanticSelector()
        self.debug_mode = True
    
    def enhance_ir_context_selection(self, ir_data: Dict, user_query: str,
                                   focus_entities: Optional[List[str]] = None,
                                   max_entities: int = 8) -> Dict[str, Any]:
        """
        Enhanced context selection using semantic understanding.
        
        This method replaces the traditional keyword-based scoring with
        intelligent semantic analysis.
        
        Args:
            ir_data: Mid-level IR data
            user_query: User's query string
            focus_entities: Optional focus entities
            max_entities: Maximum entities to select
            
        Returns:
            Enhanced context package with semantic selection
        """
        print(f"🧠 Enhanced IR Context Selection with Semantic Understanding")
        print(f"   Query: {user_query}")
        print(f"   Focus entities: {focus_entities}")
        print(f"   Max entities: {max_entities}")
        
        # Use intelligent semantic selector
        selected_components = self.semantic_selector.select_intelligent_context(
            ir_data=ir_data,
            query=user_query,
            focus_entities=focus_entities,
            max_components=max_entities
        )
        
        # Convert semantic selection to IR context format
        enhanced_context = self._convert_to_ir_context_format(
            selected_components, ir_data, user_query
        )
        
        # Add semantic analysis metadata
        enhanced_context['semantic_analysis'] = {
            'selection_method': 'intelligent_semantic',
            'query_analysis': self.semantic_selector.query_analyzer.analyze_complete_query(
                user_query, focus_entities
            ),
            'component_count': len(selected_components),
            'selection_confidence': self._calculate_selection_confidence(selected_components)
        }
        
        return enhanced_context
    
    def _convert_to_ir_context_format(self, selected_components: List[ComponentRelevanceScore],
                                    ir_data: Dict, user_query: str) -> Dict[str, Any]:
        """
        Convert semantic selection results to IR context format.
        
        Args:
            selected_components: Semantically selected components
            ir_data: Original IR data
            user_query: User query
            
        Returns:
            Context package in IR format
        """
        # Create entity map for quick lookup
        entity_map = {}
        code_context = []
        
        for module in ir_data.get('modules', []):
            module_name = module.get('module_name', '')
            
            # Process functions
            for func in module.get('functions', []):
                entity_name = func.get('name', '')
                full_name = f"{module_name}.{entity_name}"
                entity_map[full_name] = func
                entity_map[entity_name] = func  # Also map by simple name
            
            # Process classes
            for cls in module.get('classes', []):
                entity_name = cls.get('name', '')
                full_name = f"{module_name}.{entity_name}"
                entity_map[full_name] = cls
                entity_map[entity_name] = cls  # Also map by simple name
        
        # Build IR slices and code context from selected components
        ir_slices = []
        
        for component in selected_components:
            component_name = component.component_name
            
            # Find the entity in IR data
            entity_data = None
            if component_name in entity_map:
                entity_data = entity_map[component_name]
            else:
                # Try to find by partial name matching
                for key, value in entity_map.items():
                    if component_name in key or key.endswith(f".{component_name}"):
                        entity_data = value
                        break
            
            if entity_data:
                # Create IR slice
                ir_slice = {
                    'entity_name': component_name,
                    'entity_type': entity_data.get('type', 'function'),
                    'file_path': entity_data.get('file_path', ''),
                    'module_name': entity_data.get('module_name', ''),
                    'criticality': self._map_score_to_criticality(component.total_score),
                    'change_risk': 'medium',  # Default
                    'relevance_score': component.total_score,
                    'calls': entity_data.get('calls', []),
                    'used_by': entity_data.get('used_by', []),
                    'side_effects': entity_data.get('side_effects', []),
                    'semantic_explanation': component.explanation
                }
                ir_slices.append(ir_slice)
                
                # Create code context entry
                if 'source_code' in entity_data:
                    code_entry = {
                        'entity_name': component_name,
                        'file_path': entity_data.get('file_path', ''),
                        'source_code': entity_data['source_code'],
                        'relevance_score': component.total_score
                    }
                    code_context.append(code_entry)
        
        # Build the context package
        context_package = {
            'user_query': user_query,
            'task_description': f"Semantic analysis for: {user_query}",
            'task_type': 'semantic_analysis',
            'ir_slices': ir_slices,
            'code_context': code_context,
            'context_bundle': {
                'total_entities': len(ir_slices),
                'selection_method': 'semantic',
                'entities': ir_slices
            }
        }
        
        return context_package
    
    def _map_score_to_criticality(self, score: float) -> str:
        """Map relevance score to criticality level."""
        if score >= 0.8:
            return 'critical'
        elif score >= 0.6:
            return 'high'
        elif score >= 0.4:
            return 'medium'
        else:
            return 'low'
    
    def _calculate_selection_confidence(self, selected_components: List[ComponentRelevanceScore]) -> float:
        """Calculate overall confidence in the selection."""
        if not selected_components:
            return 0.0
        
        # Average of top component scores
        top_scores = [comp.total_score for comp in selected_components[:3]]
        return sum(top_scores) / len(top_scores)
    
    def create_enhanced_llm_package(self, context_package: Dict[str, Any],
                                  max_chars: int = 30000) -> str:
        """
        Create an enhanced LLM-friendly package with semantic explanations.
        
        Args:
            context_package: Context package with semantic selection
            max_chars: Maximum characters for the package
            
        Returns:
            Enhanced LLM-friendly package string
        """
        user_query = context_package.get('user_query', '')
        ir_slices = context_package.get('ir_slices', [])
        code_context = context_package.get('code_context', [])
        semantic_analysis = context_package.get('semantic_analysis', {})
        
        # Start building the package
        package_content = f"""# Intelligent Context Analysis

## Query Analysis
**User Query**: {user_query}

**Semantic Analysis**:
"""
        
        # Add query analysis if available
        query_analysis = semantic_analysis.get('query_analysis')
        if query_analysis:
            package_content += f"""- **Intent**: {query_analysis.intent.value}
- **Scope**: {query_analysis.scope.value}
- **Confidence**: {query_analysis.confidence:.2f}
- **Domain Concepts**: {len(query_analysis.domain_concepts)}
"""
            
            for concept in query_analysis.domain_concepts:
                package_content += f"  - {concept.category.title()}: {concept.concept} (confidence: {concept.confidence:.2f})\n"
        
        package_content += f"""
**Selection Method**: Intelligent Semantic Analysis (not keyword matching)
**Components Selected**: {len(ir_slices)}
**Selection Confidence**: {semantic_analysis.get('selection_confidence', 0.0):.2f}

---

## Selected Components (Ranked by Semantic Relevance)

"""
        
        # Add component details with semantic explanations
        for i, ir_slice in enumerate(ir_slices, 1):
            entity_name = ir_slice.get('entity_name', 'unknown')
            file_path = ir_slice.get('file_path', '')
            relevance_score = ir_slice.get('relevance_score', 0.0)
            explanation = ir_slice.get('semantic_explanation', 'No explanation available')
            calls = ir_slice.get('calls', [])
            used_by = ir_slice.get('used_by', [])
            
            package_content += f"""### {i}. {entity_name}
- **File**: {file_path}
- **Relevance Score**: {relevance_score:.3f}
- **Semantic Rationale**: {explanation}
- **Calls**: {calls[:5]}{'...' if len(calls) > 5 else ''} (total: {len(calls)})
- **Used By**: {used_by[:5]}{'...' if len(used_by) > 5 else ''} (total: {len(used_by)})

"""
        
        # Add code implementations
        if code_context:
            package_content += """---

## Key Implementations

"""
            
            for i, code_entry in enumerate(code_context, 1):
                entity_name = code_entry.get('entity_name', 'unknown')
                source_code = code_entry.get('source_code', '')
                
                # Truncate source code if too long
                if len(source_code) > 2000:
                    lines = source_code.split('\n')
                    truncated_lines = []
                    char_count = 0
                    for line in lines:
                        if char_count + len(line) > 2000:
                            truncated_lines.append("    # ... (implementation continues)")
                            break
                        truncated_lines.append(line)
                        char_count += len(line)
                    source_code = '\n'.join(truncated_lines)
                
                package_content += f"""### {i}. {entity_name}
```python
{source_code}
```

"""
        
        # Add methodology explanation
        package_content += """---

## Selection Methodology

This context was selected using **Intelligent Semantic Analysis** instead of simple keyword matching:

1. **Query Intent Classification**: Analyzed the semantic intent of your query
2. **Domain Concept Extraction**: Identified business and technical concepts
3. **Component Purpose Analysis**: Understood the role and purpose of each component
4. **Semantic Matching**: Matched query intent to component purposes and roles
5. **Architectural Awareness**: Considered component significance and relationships

This approach provides more relevant and architecturally coherent context than keyword-based selection.

⚠️ **Context Completeness Notice**: This context was intelligently selected based on semantic relevance. If you need additional context or different components, please refine your query or request specific components.
"""
        
        # Truncate if too long
        if len(package_content) > max_chars:
            package_content = package_content[:max_chars - 100] + "\n\n... (content truncated for size)"
        
        return package_content
    
    def integrate_with_existing_system(self, context_request_handler, ir_data: Dict,
                                     user_query: str, focus_entities: Optional[List[str]] = None,
                                     max_entities: int = 8) -> Dict[str, Any]:
        """
        Integration point with existing ContextRequestHandler.
        
        This method can be called from the existing system to replace
        keyword-based selection with semantic selection.
        """
        print("🔄 Integrating semantic selection with existing IR Context System...")
        
        # Perform semantic selection
        enhanced_context = self.enhance_ir_context_selection(
            ir_data=ir_data,
            user_query=user_query,
            focus_entities=focus_entities,
            max_entities=max_entities
        )
        
        # Create enhanced LLM package
        llm_package = self.create_enhanced_llm_package(enhanced_context)
        enhanced_context['llm_friendly_package'] = llm_package
        enhanced_context['package_size_chars'] = len(llm_package)
        
        # Add compatibility information
        if len(llm_package) <= 32000:
            enhanced_context['llm_compatibility'] = "GPT-4 compatible"
        elif len(llm_package) <= 100000:
            enhanced_context['llm_compatibility'] = "GPT-4 Turbo or Claude required"
        else:
            enhanced_context['llm_compatibility'] = "Large context LLM required"
        
        print(f"✅ Enhanced context package created:")
        print(f"   Package size: {len(llm_package):,} characters")
        print(f"   LLM compatibility: {enhanced_context['llm_compatibility']}")
        print(f"   Selected entities: {len(enhanced_context.get('ir_slices', []))}")
        
        return enhanced_context
